import { CacheManageProgressUpdateMsg, CacheManageErrorMsg, CacheManageIndexedDBDetail, CacheManageDataResultMsg, CacheManageStoreResult, CacheManageDatabaseInfo, CacheManageDatabaseResult, CacheManageWorkerReadyMsg, CacheManageGetIndexedDBDataMsg } from "@src/types/apidoc/worker.ts";

/*
|--------------------------------------------------------------------------
| 工具函数，消息发送，闲时执行
|--------------------------------------------------------------------------
*/
/**
 * 发送进度更新消息到主线程
 * @param progress - 进度百分比 (0-100)
 * @param status - 当前处理状态描述
 */
const sendProgress = (progress: number, status: string): void => {
  const message: CacheManageProgressUpdateMsg = {
    type: 'progressUpdate',
    data: {
      progress: Math.round(progress),
      status
    }
  };
  self.postMessage(message);
};

/**
 * 发送错误消息到主线程
 * @param error - 错误信息
 */
const sendError = (error: Error | string): void => {
  const message = error instanceof Error ? error.message : error;
  const stack = error instanceof Error ? error.stack : undefined;
  
  const errorMsg: CacheManageErrorMsg = {
    type: 'error',
    data: { message, stack }
  };
  
  self.postMessage(errorMsg);
};

/**
 * 发送结果数据到主线程
 * @param result - 结果数据
 */
const sendResult = (result: { totalSize: number; details: CacheManageIndexedDBDetail[] }): void => {
  const message: CacheManageDataResultMsg = {
    type: 'dataResult',
    data: result
  };
  self.postMessage(message);
};

/**
 * 使用 requestIdleCallback 在浏览器空闲时执行任务
 * 如果不支持 requestIdleCallback，则使用 setTimeout 作为降级方案
 * @param callback - 要执行的回调函数
 * @param timeout - 超时时间（毫秒）
 */
const scheduleIdleTask = (callback: () => void, timeout: number = 5000): void => {
  if (typeof self.requestIdleCallback === 'function') {
    self.requestIdleCallback(callback, { timeout });
  } else {
    // 降级方案：使用 setTimeout
    setTimeout(callback, 0);
  }
};

/*
|--------------------------------------------------------------------------
| 数据处理函数
|--------------------------------------------------------------------------
*/
/**
 * 生成存储描述信息
 * @param dbName - 数据库名称
 * @param storeName - 存储名称
 * @returns 描述信息
 */
const generateDescription = (dbName: string, storeName: string): string => {
  if (dbName === 'standaloneCache') {
    // 独立缓存数据库
    switch (storeName) {
      case 'projects': return '项目信息缓存存储';
      case 'docs': return 'API文档数据缓存存储';
      case 'commonHeaders': return '通用请求头缓存存储';
      case 'rules': return '项目规则配置缓存存储';
      case 'variables': return '项目变量缓存存储';
      default: return `独立缓存-${storeName}存储`;
    }
  } 
  
  if (dbName === 'apiflowResponseCache') {
    // API 响应缓存数据库
    return storeName === 'responseCache' 
      ? '响应结果缓存存储' 
      : `API响应缓存-${storeName}存储`;
  }
  
  return `${dbName}-${storeName}存储`;
};

/**
 * 处理单个对象存储
 * @param db - 数据库实例
 * @param storeName - 存储名称
 * @param dbName - 数据库名称
 * @param onStoreProcessed - 当对象存储处理完成时的回调
 * @returns Promise 包含 size 和 detail 的对象
 */
const processSingleStore = (
  db: IDBDatabase, 
  storeName: string, 
  dbName: string,
): Promise<CacheManageStoreResult> => {
  return new Promise((resolve, reject) => {
    // 发送开始处理存储的进度消息
    sendProgress(0, `正在处理存储: ${dbName}/${storeName}`);
    
    // 创建事务并获取对象存储
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);

    // 获取所有数据
    const request = store.getAll();

    request.onsuccess = () => {
      const allData = request.result || [];
      
      // 发送数据获取完成的进度消息
      sendProgress(50, `已获取 ${allData.length} 条数据，正在计算大小: ${dbName}/${storeName}`);

      // 在空闲时计算数据大小
      scheduleIdleTask(() => {
        try {
          // 计算当前对象存储的数据大小
          let storeSize = 0;
          const totalItems = allData.length;
          
          for (let i = 0; i < totalItems; i++) {
            const data = allData[i];
            // 将数据序列化为 JSON 字符串并计算字节大小
            const jsonString = JSON.stringify(data);
            storeSize += new Blob([jsonString]).size;
            
            // 每处理10%的数据发送一次进度更新
            if (totalItems > 10 && i % Math.ceil(totalItems / 10) === 0) {
              const progress = 50 + Math.round((i / totalItems) * 40); // 50-90%
              sendProgress(progress, `计算中 (${i + 1}/${totalItems}): ${dbName}/${storeName}`);
            }
          }

          // 生成中文描述信息
          const description = generateDescription(dbName, storeName);
          
          // 发送处理完成的进度消息
          sendProgress(100, `完成处理: ${dbName}/${storeName} (${storeSize} 字节)`);
          
          resolve({
            size: storeSize,
            detail: {
              name: `${dbName}/${storeName}`,
              size: storeSize,
              description
            }
          });
        } catch (error) {
          sendError(`处理存储 ${dbName}/${storeName} 时出错: ${error}`);
          reject(error);
        }
      });
    };

    request.onerror = () => {
      sendError(`获取存储 ${dbName}/${storeName} 数据失败: ${request.error}`);
      reject(request.error);
    };
  });
};

/**
 * 处理单个数据库
 * @param dbInfo - 数据库信息
 * @param onStoreProcessed - 当对象存储处理完成时的回调
 * @returns Promise 包含 totalSize 和 details 的对象
 */
const processSingleDatabase = (
  dbInfo: CacheManageDatabaseInfo
): Promise<CacheManageDatabaseResult> => {
  return new Promise((resolve, reject) => {
    // 打开数据库连接
    const request = indexedDB.open(dbInfo.name, dbInfo.version);
    
    request.onsuccess = async () => {
      const db = request.result;
      let totalSize = 0;
      const details: CacheManageIndexedDBDetail[] = [];
      
      try {
        // 遍历数据库中的所有对象存储
        const storeNames = Array.from(db.objectStoreNames);
        
        // 使用 Promise.all 并行处理所有存储
        const storePromises = storeNames.map(storeName => 
          processSingleStore(db, storeName, dbInfo.name)
            .catch(error => {
              console.warn(`获取对象存储 ${storeName} 数据失败:`, error);
              return null;
            })
        );

        // 等待所有存储处理完成
        const storeResults = (await Promise.all(storePromises))
          .filter(Boolean) as CacheManageStoreResult[];
        
        // 累加大小并收集详情
        for (const storeResult of storeResults) {
          totalSize += storeResult.size;
          details.push(storeResult.detail);
        }
        
        // 关闭数据库连接
        db.close();
        
        resolve({ totalSize, details });
        
      } catch (error) {
        db.close();
        reject(error);
      }
    };
    
    request.onerror = () => reject(request.error);
    request.onblocked = () => reject(new Error('数据库被阻塞'));
  });
};

/*
|--------------------------------------------------------------------------
| 主逻辑函数
|--------------------------------------------------------------------------
*/
/**
 * 获取 IndexedDB 缓存信息的主要函数
 */
const getIndexedDBData = async (): Promise<void> => {
  try {
    sendProgress(0, '开始获取 IndexedDB 数据库列表...');
    
    let totalSize = 0;
    const details: CacheManageIndexedDBDetail[] = [];
    
    // 获取所有 IndexedDB 数据库
    const databases = await indexedDB.databases();
    sendProgress(10, `发现 ${databases.length} 个数据库`);
    
    if (databases.length === 0) {
      sendResult({
        totalSize: 0,
        details: []
      });
      return;
    }
    const validDatabases = databases.filter(dbInfo => dbInfo.name);
    let totalStores = 0;
    const databaseStoreCounts: Map<string, number> = new Map();
    
    // 获取每个数据库的对象存储数量
    for (const dbInfo of validDatabases) {
      const name = dbInfo.name || '未知数据库';
      try {
        const request = indexedDB.open(name, dbInfo.version);
        const db = await new Promise<IDBDatabase>((resolve, reject) => {
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
          request.onblocked = () => reject(new Error('数据库被阻塞'));
        });
        
        const storeCount = db.objectStoreNames.length;
        databaseStoreCounts.set(name, storeCount);
        totalStores += storeCount;
        
        db.close();
      } catch (error) {
        console.warn(`无法获取数据库 ${name} 的对象存储数量:`, error);
        databaseStoreCounts.set(name, 0);
      }
    }
    sendProgress(15, `共发现 ${totalStores} 个对象存储`);
    
    // 如果没有找到任何对象存储
    if (totalStores === 0) {
      sendResult({
        totalSize: 0,
        details: []
      });
      return;
    }
    
    // 使用 Promise.all 并行处理所有数据库
    const databasePromises = validDatabases
      .map(dbInfo => {
        const dbName = dbInfo.name || '未知数据库';
        
        return processSingleDatabase({
          name: dbName,
          version: dbInfo.version
        })
          .then(dbResult => {
            totalSize += dbResult.totalSize;
            details.push(...dbResult.details);
            return dbName;
          })
          .catch(error => {
            console.warn(`处理数据库 ${dbName} 失败:`, error);
            return null;
          });
      });

    // 等待所有数据库处理完成
    await Promise.all(databasePromises);
    
    sendProgress(95, '正在整理数据...');
    
    // 按大小降序排序，方便查看占用空间最大的存储
    details.sort((a, b) => b.size - a.size);
    
    sendProgress(100, '数据获取完成');
    
    // 发送最终结果
    sendResult({
      totalSize,
      details
    });
    
  } catch (error) {
    console.error('获取 IndexedDB 缓存信息失败:', error);
    sendError(error as Error);
  } 
};

/*
|--------------------------------------------------------------------------
| Worker 初始化
|--------------------------------------------------------------------------
*/
// Worker 初始化完成
const workerReadyMsg: CacheManageWorkerReadyMsg = {
  type: 'workerReady',
  data: { message: 'IndexedDB Worker 已准备就绪' }
};
self.postMessage(workerReadyMsg);

// 监听主线程消息
self.addEventListener('message', (event: MessageEvent<CacheManageGetIndexedDBDataMsg>) => {
  const { type } = event.data;

  if (type === 'getIndexedDBData') {
    getIndexedDBData();
  } else {
    sendError(`未知的消息类型: ${type}`);
  }
});
