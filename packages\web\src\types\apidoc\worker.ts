export type CacheManageGetIndexedDBDataMsg = { type: 'getIndexedDBData' };
export type CacheManageProgressUpdateMsg = { 
  type: 'progressUpdate';
  data: { 
    progress: number; 
    status: string 
  } 
};
export type CacheManageDataResultMsg = { 
  type: 'dataResult'; 
  data: { 
    totalSize: number; 
    details: CacheManageIndexedDBDetail[] 
  } 
};
export type CacheManageErrorMsg = { 
  type: 'error'; 
  data: { 
    message: string; 
    stack?: string 
  } 
};
export type CacheManageWorkerReadyMsg = { 
  type: 'workerReady'; 
  data: { 
    message: string 
  } 
};

// 联合所有消息类型
export type CacheManageWorkerMessage = 
  | CacheManageGetIndexedDBDataMsg
  | CacheManageProgressUpdateMsg
  | CacheManageDataResultMsg
  | CacheManageErrorMsg
  | CacheManageWorkerReadyMsg;

/**
 * 数据库信息
 */
export type CacheManageDatabaseInfo = {
  name: string;
  version?: number;
}

/**
 * IndexedDB 详情项
 */
export type CacheManageIndexedDBDetail = {
  name: string;
  size: number;
  description: string;
}

/**
 * 数据库处理结果
 */
export type CacheManageDatabaseResult = {
  totalSize: number;
  details: CacheManageIndexedDBDetail[];
}

/**
 * 存储处理结果
 */
export type CacheManageStoreResult = {
  size: number;
  detail: CacheManageIndexedDBDetail;
}